.layout-navbar .navbar-dropdown.dropdown-notifications .dropdown-notifications-list .dropdown-notifications-item.marked-as-read .dropdown-notifications-read span {
    background-color: transparent !important;
}


/* ===== ACTIVE MENU STATE STYLING ===== */

/* Active menu item styling */
.menu-item.active {
    background-color: rgba(var(--bs-primary-rgb), 0.08);
    border-radius: 0.375rem;
    margin: 0.125rem 0.75rem;
}

/* Active menu link styling */
.menu-link.active {
    color: rgb(var(--bs-primary-rgb)) !important;
    background-color: rgba(var(--bs-primary-rgb), 0.08);
    font-weight: 600;
}

/* Active menu icon styling */
.menu-item.active .menu-icon {
    color: rgb(var(--bs-primary-rgb)) !important;
}

/* Active menu text styling */
.menu-item.active .menu-link > div {
    color: rgb(var(--bs-primary-rgb)) !important;
    font-weight: 600;
}

/* Hover effect for active items */
.menu-item.active .menu-link:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.12);
    color: rgb(var(--bs-primary-rgb)) !important;
}

/* Active indicator badge */
.menu-item.active .badge {
    animation: pulse 2s infinite;
}

/* Pulse animation for active indicator */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Smooth transitions for menu items */
.menu-item {
    transition: all 0.2s ease-in-out;
}

.menu-link {
    transition: all 0.2s ease-in-out;
}

/* Enhanced focus states for accessibility */
.menu-link:focus {
    outline: 2px solid rgb(var(--bs-primary-rgb));
    outline-offset: 2px;
}